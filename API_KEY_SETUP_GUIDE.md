# YouTube API 密钥配置完整指南

本指南将详细说明如何获取和配置YouTube API密钥，确保项目正常运行。

## 📍 1. 需要配置API密钥的文件位置

### 主要配置文件
- **文件路径**: `backend/.env`
- **变量名**: `YOUTUBE_API_KEY`
- **配置格式**: `YOUTUBE_API_KEY=你的API密钥`

### 相关文件说明
- `backend/.env.example` - 环境变量模板文件
- `backend/fetch_youtube_data.py` - 使用API密钥获取YouTube数据
- `backend/app.py` - Flask应用（间接依赖，通过数据库访问数据）

## 📝 2. 具体配置格式和变量名

### 2.1 环境变量文件配置

**文件**: `backend/.env`
```env
# YouTube API配置
YOUTUBE_API_KEY=AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# PostgreSQL数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/youtube_channels_db
```

### 2.2 Python代码中的使用方式

**文件**: `backend/fetch_youtube_data.py`
```python
from dotenv import load_dotenv
import os

load_dotenv()  # 加载.env文件
YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY')  # 获取API密钥

# 使用API密钥创建YouTube客户端
youtube = googleapiclient.discovery.build(
    "youtube", "v3", developerKey=YOUTUBE_API_KEY)
```

## 🔑 3. YouTube API密钥获取详细步骤

### 步骤1: 访问Google Cloud Console
1. 打开浏览器，访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 使用您的Google账号登录

### 步骤2: 创建或选择项目
1. 点击页面顶部的项目选择器
2. 选择现有项目或点击"新建项目"
3. 如果创建新项目：
   - 输入项目名称（如：`youtube-channel-explorer`）
   - 选择组织（可选）
   - 点击"创建"

### 步骤3: 启用YouTube Data API v3
1. 在左侧菜单中，点击"API和服务" > "库"
2. 在搜索框中输入"YouTube Data API v3"
3. 点击"YouTube Data API v3"
4. 点击"启用"按钮

### 步骤4: 创建API密钥
1. 在左侧菜单中，点击"API和服务" > "凭据"
2. 点击页面顶部的"+ 创建凭据"
3. 选择"API密钥"
4. 系统会生成一个新的API密钥，类似：`AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
5. **重要**: 立即复制这个密钥并保存到安全的地方

### 步骤5: 限制API密钥（推荐）
1. 在创建密钥后，点击"限制密钥"
2. 在"应用限制"部分，选择"HTTP引荐来源网址"或"IP地址"
3. 在"API限制"部分，选择"限制密钥"
4. 选择"YouTube Data API v3"
5. 点击"保存"

## ⚙️ 4. 配置验证方法

### 4.1 使用验证脚本
```bash
# 在项目根目录运行
python3 verify_api_key.py
```

### 4.2 手动验证步骤
1. 检查`.env`文件是否存在：
   ```bash
   ls -la backend/.env
   ```

2. 检查API密钥是否设置：
   ```bash
   cd backend
   grep YOUTUBE_API_KEY .env
   ```

3. 测试API连接：
   ```bash
   cd backend
   python3 fetch_youtube_data.py
   ```

### 4.3 验证成功的标志
- ✅ 脚本输出类似：`Added new channel: MKBHD (17,200,000 subscribers)`
- ✅ 数据库中成功插入频道数据
- ✅ 没有API错误信息

### 4.4 常见错误和解决方案

#### 错误1: `keyInvalid`
```
❌ YouTube API错误: 状态码: 400, 原因: keyInvalid
```
**解决方案**:
- 检查API密钥是否正确复制（没有多余空格）
- 确认API密钥在Google Cloud Console中有效
- 重新生成API密钥

#### 错误2: `quotaExceeded`
```
❌ YouTube API错误: 状态码: 403, 原因: quotaExceeded
```
**解决方案**:
- 等待配额重置（通常每天重置）
- 在Google Cloud Console中检查配额使用情况
- 考虑申请更高的配额限制

#### 错误3: `API not enabled`
```
❌ YouTube API错误: 状态码: 403, 原因: forbidden
```
**解决方案**:
- 确认已启用YouTube Data API v3
- 检查项目是否正确选择

## 🚀 5. 完整项目启动步骤

### 步骤顺序（重要！）

1. **安装依赖**
   ```bash
   python3 install.py
   ```

2. **配置数据库**
   ```bash
   cd backend
   python3 setup_database.py
   ```

3. **配置API密钥**
   ```bash
   # 编辑 backend/.env 文件
   nano backend/.env
   # 或使用其他编辑器
   ```

4. **验证API密钥**
   ```bash
   python3 verify_api_key.py
   ```

5. **获取初始数据**
   ```bash
   cd backend
   python3 fetch_youtube_data.py
   ```

6. **启动应用**
   ```bash
   cd ..
   python3 run.py
   ```

## 📋 6. 配置示例

### 完整的 `.env` 文件示例
```env
# YouTube API配置
YOUTUBE_API_KEY=AIzaSyBvK8x9Q2mF3nH7pL5wR8tY6uI9oP2sA4dF

# PostgreSQL数据库配置
DATABASE_URL=postgresql://youtube_user:mypassword123@localhost:5432/youtube_channels_db
```

### API密钥格式说明
- 长度：39个字符
- 前缀：`AIzaSy`
- 包含：大小写字母、数字、连字符和下划线
- 示例：`AIzaSyBvK8x9Q2mF3nH7pL5wR8tY6uI9oP2sA4dF`

## ⚠️ 7. 安全注意事项

1. **不要提交API密钥到版本控制**
   - `.env` 文件已在 `.gitignore` 中
   - 只提交 `.env.example` 模板文件

2. **定期轮换API密钥**
   - 建议每3-6个月更换一次
   - 如果怀疑密钥泄露，立即更换

3. **设置API密钥限制**
   - 限制使用的API
   - 限制访问的IP地址或域名

4. **监控API使用情况**
   - 定期检查Google Cloud Console中的使用统计
   - 设置配额警报

## 🔧 8. 故障排除

如果遇到问题，请按以下顺序检查：

1. 运行验证脚本：`python3 verify_api_key.py`
2. 检查网络连接
3. 确认Google Cloud项目状态
4. 查看详细错误日志
5. 参考本指南的错误解决方案

## 📞 9. 获取帮助

如果仍有问题，请：
1. 检查项目的 `README.md` 文件
2. 查看Google Cloud Console的错误日志
3. 参考YouTube Data API官方文档
