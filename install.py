#!/usr/bin/env python3
"""
YouTube 频道浏览器安装脚本
自动化安装和配置过程
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要 Python 3.8 或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✓ Python 版本检查通过: {sys.version}")
    return True

def check_postgresql():
    """检查PostgreSQL是否安装"""
    try:
        result = subprocess.run(['psql', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ PostgreSQL 已安装: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ PostgreSQL 未安装或未在PATH中")
    print("请安装 PostgreSQL 12+ 后重试")
    return False

def install_dependencies():
    """安装Python依赖"""
    print("正在安装Python依赖...")
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 
            'backend/requirements.txt'
        ], check=True)
        print("✓ Python 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def setup_environment():
    """设置环境配置"""
    env_example = Path('backend/.env.example')
    env_file = Path('backend/.env')
    
    if env_file.exists():
        print("✓ .env 文件已存在")
        return True
    
    if env_example.exists():
        shutil.copy(env_example, env_file)
        print("✓ 已创建 .env 文件")
        print("请编辑 backend/.env 文件，设置您的配置:")
        print("  - YOUTUBE_API_KEY: YouTube API 密钥")
        print("  - DATABASE_URL: PostgreSQL 数据库连接字符串")
        return True
    else:
        print("❌ .env.example 文件不存在")
        return False

def main():
    """主安装流程"""
    print("=== YouTube 频道浏览器安装脚本 ===\n")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查PostgreSQL
    if not check_postgresql():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 设置环境
    if not setup_environment():
        sys.exit(1)
    
    print("\n=== 安装完成 ===")
    print("下一步:")
    print("1. 编辑 backend/.env 文件，设置API密钥和数据库连接")
    print("2. 运行 'cd backend && python setup_database.py' 设置数据库")
    print("3. 运行 'cd backend && python fetch_youtube_data.py' 获取初始数据")
    print("4. 运行 'python run.py' 启动应用")

if __name__ == '__main__':
    main()
