# run.py
import subprocess
import webbrowser
import os
import time
import sys
import signal
import requests
from pathlib import Path

# --- 配置 ---
BACKEND_DIR = 'backend'
FRONTEND_FILE = 'frontend/index.html'
FLASK_APP = 'app.py'
HOST = '127.0.0.1'
PORT = 5000
API_URL = f'http://{HOST}:{PORT}/api/health'

def check_dependencies():
    """检查必要的依赖和文件"""
    print("--- 检查项目依赖 ---")

    # 检查后端目录
    if not os.path.exists(BACKEND_DIR):
        print(f"错误: 后端目录 '{BACKEND_DIR}' 不存在")
        return False

    # 检查前端文件
    if not os.path.exists(FRONTEND_FILE):
        print(f"错误: 前端文件 '{FRONTEND_FILE}' 不存在")
        return False

    # 检查.env文件
    env_file = os.path.join(BACKEND_DIR, '.env')
    if not os.path.exists(env_file):
        print(f"警告: 环境配置文件 '{env_file}' 不存在")
        print("请复制 .env.example 为 .env 并配置相关参数")
        return False

    print("依赖检查完成")
    return True

def wait_for_server(url, timeout=30):
    """等待服务器启动"""
    print(f"等待服务器启动 ({url})...")
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("服务器已启动")
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(1)

    print("服务器启动超时")
    return False

def main():
    """主函数"""
    print("=== YouTube 频道浏览器启动脚本 ===")

    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，请解决上述问题后重试")
        sys.exit(1)

    print("--- 启动后端 Flask 服务 ---")

    # 启动后端服务
    backend_process = None
    try:
        # 使用 python 命令启动 Flask 应用
        backend_process = subprocess.Popen(
            [sys.executable, FLASK_APP],
            cwd=os.path.abspath(BACKEND_DIR),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        print(f"后端服务进程已启动，PID: {backend_process.pid}")

        # 等待服务器启动
        if not wait_for_server(API_URL):
            print("服务器启动失败，请检查错误信息")
            if backend_process.poll() is not None:
                stdout, stderr = backend_process.communicate()
                print("STDOUT:", stdout.decode())
                print("STDERR:", stderr.decode())
            return

        # --- 自动打开网页进行测试 ---
        frontend_path = os.path.abspath(FRONTEND_FILE)
        url = f"file://{frontend_path}"
        print(f"--- 在默认浏览器中打开前端页面 ---")
        print(f"URL: {url}")
        webbrowser.open(url)

        print("\n=== 应用已启动 ===")
        print(f"后端API地址: http://{HOST}:{PORT}")
        print("前端页面已在浏览器中打开")
        print("要停止服务，请按 Ctrl+C")

        # 等待用户中断
        try:
            backend_process.wait()
        except KeyboardInterrupt:
            print("\n--- 收到 Ctrl+C，正在关闭服务 ---")

    except Exception as e:
        print(f"启动失败: {e}")
    finally:
        # 清理后端进程
        if backend_process and backend_process.poll() is None:
            print("正在关闭后端服务...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("强制关闭后端服务...")
                backend_process.kill()
                backend_process.wait()
            print("后端服务已关闭")

if __name__ == '__main__':
    main()