# YouTube 频道浏览器

一个基于 Flask 和原生 JavaScript 的 YouTube 频道数据浏览器，支持多维度筛选和实时搜索。

## 功能特性

- 🔍 **多维度筛选**: 支持按订阅者数量、创建时间、频道名称筛选
- 📊 **实时搜索**: 输入即搜索，无需点击按钮
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🚀 **高性能**: 数据库索引优化，查询速度快
- 🎨 **现代化UI**: 简洁美观的用户界面

## 技术栈

### 后端
- **Flask**: Web 框架
- **Flask-SQLAlchemy**: ORM 数据库操作
- **PostgreSQL**: 数据库
- **YouTube Data API v3**: 数据源

### 前端
- **原生 JavaScript**: 无框架依赖
- **CSS Grid/Flexbox**: 响应式布局
- **Fetch API**: 异步数据请求

## 项目结构

```
youtube-channel-explorer/
├── backend/
│   ├── app.py                 # Flask 主应用
│   ├── models.py              # 数据库模型
│   ├── fetch_youtube_data.py  # YouTube 数据获取脚本
│   ├── setup_database.py      # 数据库设置脚本
│   ├── requirements.txt       # Python 依赖
│   ├── .env.example          # 环境变量模板
│   └── .env                  # 环境变量配置（需创建）
├── frontend/
│   ├── index.html            # 主页面
│   ├── style.css             # 样式文件
│   └── script.js             # JavaScript 逻辑
├── run.py                    # 项目启动脚本
└── README.md                 # 项目说明
```

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- Python 3.8+
- PostgreSQL 12+
- Git

### 2. 克隆项目

```bash
git clone <repository-url>
cd youtube-channel-explorer
```

### 3. 安装 Python 依赖

```bash
cd backend
pip install -r requirements.txt
```

### 4. 配置数据库

#### 方法一：使用自动化脚本（推荐）

```bash
cd backend
python setup_database.py
```

按提示输入 PostgreSQL 管理员凭据和新数据库配置。

#### 方法二：手动配置

1. 创建数据库：
```sql
CREATE DATABASE youtube_channels_db;
CREATE USER youtube_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE youtube_channels_db TO youtube_user;
```

2. 复制环境变量文件：
```bash
cp .env.example .env
```

3. 编辑 `.env` 文件：
```env
YOUTUBE_API_KEY=your_youtube_api_key_here
DATABASE_URL=postgresql://youtube_user:your_password@localhost:5432/youtube_channels_db
```

### 5. 获取和配置 YouTube API 密钥

#### 方法一：使用交互式配置助手（推荐）

```bash
python3 setup_api_key.py
```

这个脚本将引导您完成整个API密钥配置过程。

#### 方法二：手动配置

**步骤1: 获取API密钥**
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 转到 "API和服务" > "库"
4. 搜索并启用 "YouTube Data API v3"
5. 转到 "API和服务" > "凭据"
6. 点击 "+ 创建凭据" > "API密钥"
7. 复制生成的API密钥

**步骤2: 配置API密钥**
编辑 `backend/.env` 文件：
```env
YOUTUBE_API_KEY=你的API密钥
DATABASE_URL=postgresql://username:password@localhost:5432/youtube_channels_db
```

**步骤3: 验证配置**
```bash
python3 verify_api_key.py
```

### 6. 初始化数据

```bash
cd backend
python fetch_youtube_data.py
```

### 7. 启动应用

返回项目根目录：
```bash
cd ..
python run.py
```

应用将自动：
- 启动 Flask 后端服务（端口 5000）
- 在默认浏览器中打开前端页面

## API 接口

### 获取频道列表
```
GET /api/channels
```

查询参数：
- `min_subs`: 最小订阅者数量
- `max_subs`: 最大订阅者数量
- `start_year`: 开始年份
- `start_month`: 开始月份
- `end_year`: 结束年份
- `end_month`: 结束月份
- `name`: 频道名称（模糊匹配）

### 健康检查
```
GET /api/health
```

### 获取统计信息
```
GET /api/stats
```

## 数据库优化

项目使用了以下索引优化查询性能：

- `idx_subscriber_count`: 订阅者数量索引
- `idx_created_at`: 创建时间索引
- `idx_subscriber_created`: 复合索引（订阅者数量 + 创建时间）
- `idx_name`: 频道名称索引

## 开发说明

### 添加新的频道

编辑 `backend/fetch_youtube_data.py` 中的 `CHANNEL_IDS` 列表，添加新的 YouTube 频道 ID。

### 自定义样式

修改 `frontend/style.css` 文件来自定义界面样式。

### 扩展 API

在 `backend/app.py` 中添加新的路由和功能。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 服务是否启动
   - 验证 `.env` 文件中的数据库配置

2. **YouTube API 配额超限**
   - 检查 API 密钥是否有效
   - 确认 API 配额未超限

3. **前端无法加载数据**
   - 确认后端服务正在运行
   - 检查浏览器控制台的错误信息

### 日志查看

后端日志会显示在启动 `run.py` 的终端中。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！