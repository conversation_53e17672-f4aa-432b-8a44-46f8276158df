// frontend/script.js
document.addEventListener('DOMContentLoaded', () => {
    const API_URL = 'http://127.0.0.1:5000/api';

    // 获取所有DOM元素
    const minSubsInput = document.getElementById('min-subs');
    const maxSubsInput = document.getElementById('max-subs');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const channelNameInput = document.getElementById('channel-name');
    const channelList = document.getElementById('channel-list');
    const sortBySelect = document.getElementById('sort-by');
    const clearFiltersBtn = document.getElementById('clear-filters');
    const refreshDataBtn = document.getElementById('refresh-data');
    const statsInfo = document.getElementById('stats-info');
    const totalChannelsSpan = document.getElementById('total-channels');

    // 筛选器元素数组
    const filters = [minSubsInput, maxSubsInput, startDateInput, endDateInput, channelNameInput];

    // 当前数据缓存
    let currentChannels = [];

    // 为每个筛选器添加事件监听器
    filters.forEach(input => {
        input.addEventListener('input', debounce(fetchChannels, 300));
    });

    // 排序选择器事件监听
    sortBySelect.addEventListener('change', () => {
        if (currentChannels.length > 0) {
            renderChannels(sortChannels(currentChannels));
        }
    });

    // 清除筛选器按钮
    clearFiltersBtn.addEventListener('click', clearAllFilters);

    // 刷新数据按钮
    refreshDataBtn.addEventListener('click', () => {
        fetchChannels();
        fetchStats();
    });

    // 防抖函数，防止过于频繁地触发API调用
    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    // 清除所有筛选器
    function clearAllFilters() {
        minSubsInput.value = '';
        maxSubsInput.value = '';
        startDateInput.value = '';
        endDateInput.value = '';
        channelNameInput.value = '';
        sortBySelect.value = 'subscribers_desc';
        fetchChannels();
    }

    // 获取统计信息
    async function fetchStats() {
        try {
            const response = await fetch(`${API_URL}/stats`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const stats = await response.json();
            totalChannelsSpan.textContent = `共 ${stats.total_channels} 个频道`;
        } catch (error) {
            console.error("Failed to fetch stats:", error);
            totalChannelsSpan.textContent = "统计信息加载失败";
        }
    }

    // 获取频道数据
    async function fetchChannels() {
        // 显示加载状态
        channelList.innerHTML = '<p class="loading">正在加载频道数据...</p>';

        // 1. 构建查询参数
        const params = new URLSearchParams();

        if (minSubsInput.value) params.append('min_subs', minSubsInput.value * 10000);
        if (maxSubsInput.value) params.append('max_subs', maxSubsInput.value * 10000);

        if (startDateInput.value) {
            const [year, month] = startDateInput.value.split('-');
            params.append('start_year', year);
            params.append('start_month', month);
        }
        if (endDateInput.value) {
            const [year, month] = endDateInput.value.split('-');
            params.append('end_year', year);
            params.append('end_month', month);
        }

        if (channelNameInput.value.trim()) {
            params.append('name', channelNameInput.value.trim());
        }

        // 2. 发送API请求
        try {
            const response = await fetch(`${API_URL}/channels?${params.toString()}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            currentChannels = data;
            renderChannels(sortChannels(data));
        } catch (error) {
            console.error("Failed to fetch channels:", error);
            channelList.innerHTML = `<p class="error">加载数据失败，请检查后端服务是否运行。<br>错误信息: ${error.message}</p>`;
        }
    }

    // 排序频道数据
    function sortChannels(channels) {
        const sortBy = sortBySelect.value;
        const sortedChannels = [...channels];

        switch (sortBy) {
            case 'subscribers_desc':
                return sortedChannels.sort((a, b) => b.subscriber_count - a.subscriber_count);
            case 'subscribers_asc':
                return sortedChannels.sort((a, b) => a.subscriber_count - b.subscriber_count);
            case 'name_asc':
                return sortedChannels.sort((a, b) => a.name.localeCompare(b.name));
            case 'created_desc':
                return sortedChannels.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            case 'created_asc':
                return sortedChannels.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
            default:
                return sortedChannels;
        }
    }

    // 格式化订阅者数量
    function formatSubscriberCount(count) {
        if (count >= 10000) {
            return (count / 10000).toFixed(1) + ' 万';
        } else if (count >= 1000) {
            return (count / 1000).toFixed(1) + ' 千';
        } else {
            return count.toString();
        }
    }

    // 格式化日期
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // 渲染频道列表
    function renderChannels(channels) {
        channelList.innerHTML = ''; // 清空现有列表

        if (channels.length === 0) {
            channelList.innerHTML = '<p class="no-results">未找到符合条件的频道。</p>';
            return;
        }

        channels.forEach(channel => {
            const item = document.createElement('div');
            item.className = 'channel-item';

            const subscriberCountFormatted = formatSubscriberCount(channel.subscriber_count);
            const createdAtFormatted = formatDate(channel.created_at);

            item.innerHTML = `
                <div class="channel-info">
                    <div class="name">${escapeHtml(channel.name)}</div>
                    <div class="details">
                        <span class="subscriber-count">订阅者: ${subscriberCountFormatted}</span>
                        <span class="created-date">创建于: ${createdAtFormatted}</span>
                    </div>
                </div>
            `;
            channelList.appendChild(item);
        });
    }

    // HTML转义函数，防止XSS攻击
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    // 页面加载时初始化
    fetchChannels();
    fetchStats();

    // 定期刷新统计信息（每5分钟）
    setInterval(fetchStats, 5 * 60 * 1000);
});