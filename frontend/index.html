<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube 频道浏览器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>YouTube 频道浏览器</h1>
            <div class="stats" id="stats-info">
                <span id="total-channels">加载中...</span>
            </div>
        </header>

        <aside class="filters">
            <h2>筛选器</h2>
            <div class="filter-group">
                <label>订阅者数量 (万)</label>
                <div class="range-inputs">
                    <input type="number" id="min-subs" placeholder="最低(如 100)" min="0">
                    <span>-</span>
                    <input type="number" id="max-subs" placeholder="最高(如 200)" min="0">
                </div>
            </div>
            <div class="filter-group">
                <label>创建年月范围</label>
                <div class="date-inputs">
                   <input type="month" id="start-date" min="2005-01" max="2024-12">
                   <span>-</span>
                   <input type="month" id="end-date" min="2005-01" max="2024-12">
                </div>
            </div>
            <div class="filter-group">
                <label for="channel-name">频道名称 (模糊查询)</label>
                <input type="text" id="channel-name" placeholder="可选, 如 'Tech'">
            </div>
            <div class="filter-actions">
                <button id="clear-filters" class="btn-secondary">清除筛选</button>
                <button id="refresh-data" class="btn-primary">刷新数据</button>
            </div>
        </aside>

        <main class="content">
            <div class="content-header">
                <h2>频道列表</h2>
                <div class="sort-options">
                    <label for="sort-by">排序方式:</label>
                    <select id="sort-by">
                        <option value="subscribers_desc">订阅者数量 (高到低)</option>
                        <option value="subscribers_asc">订阅者数量 (低到高)</option>
                        <option value="name_asc">频道名称 (A-Z)</option>
                        <option value="created_desc">创建时间 (新到旧)</option>
                        <option value="created_asc">创建时间 (旧到新)</option>
                    </select>
                </div>
            </div>
            <div id="channel-list">
                <p class="loading">正在加载频道数据...</p>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>