/* frontend/style.css */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    background-color: #f4f7f9;
    color: #333;
    line-height: 1.6;
}

.container {
    display: grid;
    grid-template-columns: 300px 1fr;
    grid-template-rows: auto 1fr;
    gap: 20px;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    grid-template-areas:
        "header header"
        "filters content";
}

header {
    grid-area: header;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filters {
    grid-area: filters;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    align-self: start;
    position: sticky;
    top: 20px;
}

.content {
    grid-area: content;
}

h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
}

h2 {
    margin-top: 0;
    color: #34495e;
    font-size: 1.3rem;
}

.stats {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.filter-group {
    margin-bottom: 20px;
}

.filter-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.filter-group input, .filter-group select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.filter-group input:focus, .filter-group select:focus {
    outline: none;
    border-color: #3498db;
}

.range-inputs, .date-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.range-inputs input, .date-inputs input {
    width: calc(50% - 15px);
}

.range-inputs span, .date-inputs span {
    color: #7f8c8d;
    font-weight: 500;
}

.filter-actions {
    margin-top: 25px;
    display: flex;
    gap: 10px;
}

.btn-primary, .btn-secondary {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    flex: 1;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-options label {
    font-weight: 500;
    color: #2c3e50;
}

.sort-options select {
    padding: 8px 12px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    background: white;
}

#channel-list {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.channel-item {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.channel-item:hover {
    background-color: #f8f9fa;
}

.channel-item:last-child {
    border-bottom: none;
}

.channel-info {
    flex: 1;
}

.channel-item .name {
    font-size: 1.2em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.channel-item .details {
    color: #7f8c8d;
    font-size: 0.9em;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.channel-item .subscriber-count {
    font-weight: 500;
    color: #e74c3c;
}

.channel-item .created-date {
    color: #95a5a6;
}

.loading, .no-results, .error {
    text-align: center;
    color: #7f8c8d;
    padding: 40px 20px;
    font-size: 1.1em;
}

.error {
    color: #e74c3c;
    background-color: #fdf2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    margin: 20px;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #3498db;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 280px 1fr;
        gap: 15px;
        padding: 15px;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .sort-options {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-areas:
            "header"
            "filters"
            "content";
        gap: 15px;
        padding: 10px;
    }

    .filters {
        position: static;
    }

    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    h1 {
        font-size: 1.5rem;
    }

    .channel-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .channel-item .details {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
    }

    .filter-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .range-inputs, .date-inputs {
        flex-direction: column;
        gap: 8px;
    }

    .range-inputs input, .date-inputs input {
        width: 100%;
    }

    .range-inputs span, .date-inputs span {
        display: none;
    }

    .channel-item .details {
        flex-direction: column;
        align-items: flex-start;
    }
}