#!/usr/bin/env python3
"""
YouTube API 密钥验证脚本
检查API密钥配置是否正确并测试连接
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import googleapiclient.discovery
from googleapiclient.errors import HttpError

def check_env_file():
    """检查.env文件是否存在"""
    env_file = Path('backend/.env')
    if not env_file.exists():
        print("❌ 错误: backend/.env 文件不存在")
        print("请先运行以下命令创建配置文件:")
        print("  cd backend")
        print("  cp .env.example .env")
        return False
    
    print("✓ backend/.env 文件存在")
    return True

def load_api_key():
    """加载API密钥"""
    # 切换到backend目录加载.env文件
    original_dir = os.getcwd()
    try:
        os.chdir('backend')
        load_dotenv()
        api_key = os.getenv('YOUTUBE_API_KEY')
        
        if not api_key:
            print("❌ 错误: YOUTUBE_API_KEY 未在.env文件中设置")
            return None
        
        if api_key == 'your_youtube_api_key_here':
            print("❌ 错误: YOUTUBE_API_KEY 仍为默认值，请设置真实的API密钥")
            return None
        
        print("✓ YOUTUBE_API_KEY 已设置")
        return api_key
    finally:
        os.chdir(original_dir)

def test_api_connection(api_key):
    """测试API连接"""
    print("正在测试YouTube API连接...")
    
    try:
        # 创建YouTube API客户端
        youtube = googleapiclient.discovery.build(
            "youtube", "v3", developerKey=api_key)
        
        # 测试API调用 - 获取一个知名频道的信息
        test_channel_id = 'UCBJycsmduvYEL83R_U4JriQ'  # MKBHD
        request = youtube.channels().list(
            part="snippet,statistics",
            id=test_channel_id
        )
        response = request.execute()
        
        if response.get('items'):
            channel = response['items'][0]
            channel_name = channel['snippet']['title']
            subscriber_count = channel['statistics'].get('subscriberCount', 'N/A')
            print(f"✅ API连接成功!")
            print(f"   测试频道: {channel_name}")
            print(f"   订阅者数: {subscriber_count}")
            return True
        else:
            print("❌ API调用成功但未返回数据")
            return False
            
    except HttpError as e:
        error_details = e.error_details[0] if e.error_details else {}
        reason = error_details.get('reason', 'Unknown')
        message = error_details.get('message', str(e))
        
        print(f"❌ YouTube API错误:")
        print(f"   状态码: {e.resp.status}")
        print(f"   原因: {reason}")
        print(f"   消息: {message}")
        
        if reason == 'keyInvalid':
            print("\n🔧 解决方案:")
            print("   1. 检查API密钥是否正确复制")
            print("   2. 确认API密钥在Google Cloud Console中有效")
            print("   3. 检查API密钥是否启用了YouTube Data API v3")
        elif reason == 'quotaExceeded':
            print("\n🔧 解决方案:")
            print("   1. API配额已用完，请等待重置或增加配额")
            print("   2. 检查Google Cloud Console中的配额使用情况")
        
        return False
        
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

def main():
    """主验证流程"""
    print("=== YouTube API 密钥验证 ===\n")
    
    # 检查.env文件
    if not check_env_file():
        return 1
    
    # 加载API密钥
    api_key = load_api_key()
    if not api_key:
        print("\n🔧 请按以下步骤配置API密钥:")
        print("1. 编辑 backend/.env 文件")
        print("2. 将 YOUTUBE_API_KEY=your_youtube_api_key_here")
        print("3. 替换为 YOUTUBE_API_KEY=你的真实API密钥")
        return 1
    
    # 测试API连接
    if test_api_connection(api_key):
        print("\n🎉 API密钥配置验证成功!")
        print("现在可以运行以下命令获取YouTube数据:")
        print("  cd backend")
        print("  python fetch_youtube_data.py")
        return 0
    else:
        print("\n❌ API密钥验证失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
