#!/usr/bin/env python3
"""
YouTube API 密钥交互式配置助手
引导用户完成API密钥的获取和配置过程
"""

import os
import sys
import webbrowser
from pathlib import Path
import re

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🔑 YouTube API 密钥配置助手")
    print("=" * 60)
    print()

def check_env_file():
    """检查并创建.env文件"""
    env_file = Path('backend/.env')
    env_example = Path('backend/.env.example')
    
    if not env_example.exists():
        print("❌ 错误: backend/.env.example 文件不存在")
        return False
    
    if not env_file.exists():
        print("📝 创建 .env 文件...")
        # 复制模板文件
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ .env 文件已创建")
    else:
        print("✅ .env 文件已存在")
    
    return True

def validate_api_key(api_key):
    """验证API密钥格式"""
    if not api_key:
        return False, "API密钥不能为空"
    
    if api_key == 'your_youtube_api_key_here':
        return False, "请替换为真实的API密钥"
    
    # YouTube API密钥格式验证
    if not api_key.startswith('AIzaSy'):
        return False, "YouTube API密钥应以 'AIzaSy' 开头"
    
    if len(api_key) != 39:
        return False, f"YouTube API密钥长度应为39个字符，当前为{len(api_key)}个字符"
    
    # 检查是否包含有效字符
    if not re.match(r'^[A-Za-z0-9_-]+$', api_key):
        return False, "API密钥包含无效字符"
    
    return True, "API密钥格式正确"

def update_env_file(api_key):
    """更新.env文件中的API密钥"""
    env_file = Path('backend/.env')
    
    try:
        # 读取现有内容
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换API密钥
        if 'YOUTUBE_API_KEY=' in content:
            # 使用正则表达式替换
            content = re.sub(
                r'YOUTUBE_API_KEY=.*',
                f'YOUTUBE_API_KEY={api_key}',
                content
            )
        else:
            # 如果不存在，添加到文件开头
            content = f'YOUTUBE_API_KEY={api_key}\n\n' + content
        
        # 写回文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"❌ 更新.env文件失败: {e}")
        return False

def guide_api_key_creation():
    """引导用户创建API密钥"""
    print("🔗 获取YouTube API密钥步骤:")
    print()
    print("1. 访问 Google Cloud Console")
    print("2. 创建或选择项目")
    print("3. 启用 YouTube Data API v3")
    print("4. 创建 API 密钥")
    print()
    
    choice = input("是否要打开 Google Cloud Console？(y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        print("🌐 正在打开 Google Cloud Console...")
        webbrowser.open('https://console.cloud.google.com/')
        print()
    
    print("📋 详细步骤:")
    print("   1. 在 Google Cloud Console 中创建新项目")
    print("   2. 转到 'API和服务' > '库'")
    print("   3. 搜索并启用 'YouTube Data API v3'")
    print("   4. 转到 'API和服务' > '凭据'")
    print("   5. 点击 '+ 创建凭据' > 'API密钥'")
    print("   6. 复制生成的API密钥")
    print()

def main():
    """主配置流程"""
    print_header()
    
    # 检查.env文件
    if not check_env_file():
        return 1
    
    # 检查当前API密钥状态
    env_file = Path('backend/.env')
    current_key = None
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            match = re.search(r'YOUTUBE_API_KEY=(.+)', content)
            if match:
                current_key = match.group(1).strip()
    except Exception:
        pass
    
    if current_key and current_key != 'your_youtube_api_key_here':
        print(f"📍 当前API密钥: {current_key[:10]}...{current_key[-5:]}")
        choice = input("是否要更新API密钥？(y/n): ").lower().strip()
        if choice not in ['y', 'yes', '是']:
            print("✅ 保持当前配置")
            return 0
    
    # 引导获取API密钥
    guide_api_key_creation()
    
    # 获取用户输入的API密钥
    while True:
        print("🔑 请输入您的YouTube API密钥:")
        api_key = input("API密钥: ").strip()
        
        if not api_key:
            print("❌ API密钥不能为空，请重新输入")
            continue
        
        # 验证API密钥格式
        is_valid, message = validate_api_key(api_key)
        if not is_valid:
            print(f"❌ {message}")
            retry = input("是否重新输入？(y/n): ").lower().strip()
            if retry not in ['y', 'yes', '是']:
                print("❌ 配置已取消")
                return 1
            continue
        
        print(f"✅ {message}")
        break
    
    # 更新.env文件
    if update_env_file(api_key):
        print("✅ API密钥已保存到 backend/.env 文件")
    else:
        print("❌ 保存API密钥失败")
        return 1
    
    # 提示下一步
    print()
    print("🎉 API密钥配置完成！")
    print()
    print("📋 下一步操作:")
    print("1. 验证API密钥: python3 verify_api_key.py")
    print("2. 获取YouTube数据: cd backend && python3 fetch_youtube_data.py")
    print("3. 启动应用: python3 run.py")
    print()
    
    # 询问是否立即验证
    choice = input("是否立即验证API密钥？(y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        print("🔍 正在验证API密钥...")
        os.system('python3 verify_api_key.py')
    
    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n❌ 配置已取消")
        sys.exit(1)
