#!/usr/bin/env python3
"""
项目完整性验证脚本
检查所有必要文件是否存在且格式正确
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (缺失)")
        return False

def check_file_content(file_path, required_content, description):
    """检查文件内容是否包含必要内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if all(req in content for req in required_content):
                print(f"✓ {description}: 内容检查通过")
                return True
            else:
                print(f"❌ {description}: 缺少必要内容")
                return False
    except Exception as e:
        print(f"❌ {description}: 读取失败 - {e}")
        return False

def main():
    """主验证流程"""
    print("=== YouTube 频道浏览器项目验证 ===\n")
    
    all_good = True
    
    # 检查后端文件
    print("检查后端文件:")
    backend_files = [
        ("backend/app.py", "Flask 主应用"),
        ("backend/models.py", "数据库模型"),
        ("backend/fetch_youtube_data.py", "YouTube 数据获取脚本"),
        ("backend/setup_database.py", "数据库设置脚本"),
        ("backend/requirements.txt", "Python 依赖文件"),
        ("backend/.env.example", "环境变量模板")
    ]
    
    for file_path, desc in backend_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 检查前端文件
    print("\n检查前端文件:")
    frontend_files = [
        ("frontend/index.html", "HTML 主页面"),
        ("frontend/style.css", "CSS 样式文件"),
        ("frontend/script.js", "JavaScript 逻辑文件")
    ]
    
    for file_path, desc in frontend_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 检查根目录文件
    print("\n检查根目录文件:")
    root_files = [
        ("run.py", "项目启动脚本"),
        ("install.py", "安装脚本"),
        ("README.md", "项目说明文档")
    ]
    
    for file_path, desc in root_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 检查关键文件内容
    print("\n检查关键文件内容:")
    
    # 检查 requirements.txt
    if not check_file_content("backend/requirements.txt", 
                            ["Flask", "Flask-SQLAlchemy", "Flask-CORS", "psycopg2-binary"],
                            "requirements.txt 依赖包"):
        all_good = False
    
    # 检查 models.py
    if not check_file_content("backend/models.py",
                            ["class Channel", "db.Model", "Index"],
                            "models.py 数据库模型"):
        all_good = False
    
    # 检查 app.py
    if not check_file_content("backend/app.py",
                            ["Flask", "@app.route", "/api/channels"],
                            "app.py Flask 应用"):
        all_good = False
    
    # 检查前端文件
    if not check_file_content("frontend/index.html",
                            ["YouTube 频道浏览器", "筛选器", "channel-list"],
                            "index.html HTML 结构"):
        all_good = False
    
    if not check_file_content("frontend/style.css",
                            [".container", ".filters", ".channel-item"],
                            "style.css CSS 样式"):
        all_good = False
    
    if not check_file_content("frontend/script.js",
                            ["fetchChannels", "renderChannels", "API_URL"],
                            "script.js JavaScript 逻辑"):
        all_good = False
    
    # 总结
    print("\n=== 验证结果 ===")
    if all_good:
        print("✅ 项目验证通过！所有必要文件都存在且内容正确。")
        print("\n下一步:")
        print("1. 运行 'python install.py' 安装依赖")
        print("2. 配置 backend/.env 文件")
        print("3. 设置 PostgreSQL 数据库")
        print("4. 运行 'python run.py' 启动应用")
    else:
        print("❌ 项目验证失败！请检查上述缺失的文件或内容。")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
