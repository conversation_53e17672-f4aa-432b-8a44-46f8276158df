#!/usr/bin/env python3
"""
数据库设置脚本
用于创建PostgreSQL数据库和用户
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import getpass
import sys

def create_database():
    """创建数据库和用户"""
    print("=== PostgreSQL数据库设置 ===")
    print("请确保PostgreSQL服务已启动")
    
    # 获取管理员凭据
    admin_user = input("请输入PostgreSQL管理员用户名 (默认: postgres): ").strip() or "postgres"
    admin_password = getpass.getpass("请输入PostgreSQL管理员密码: ")
    
    # 数据库配置
    db_name = "youtube_channels_db"
    db_user = input(f"请输入新数据库用户名 (默认: {db_name}_user): ").strip() or f"{db_name}_user"
    db_password = getpass.getpass("请输入新数据库用户密码: ")
    
    try:
        # 连接到PostgreSQL
        conn = psycopg2.connect(
            host="localhost",
            user=admin_user,
            password=admin_password,
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 创建数据库
        print(f"创建数据库: {db_name}")
        cursor.execute(f"DROP DATABASE IF EXISTS {db_name}")
        cursor.execute(f"CREATE DATABASE {db_name}")
        
        # 创建用户
        print(f"创建用户: {db_user}")
        cursor.execute(f"DROP USER IF EXISTS {db_user}")
        cursor.execute(f"CREATE USER {db_user} WITH PASSWORD '{db_password}'")
        
        # 授权
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {db_name} TO {db_user}")
        
        cursor.close()
        conn.close()
        
        # 生成.env文件
        env_content = f"""# YouTube API配置
YOUTUBE_API_KEY=your_youtube_api_key_here

# PostgreSQL数据库配置
DATABASE_URL=postgresql://{db_user}:{db_password}@localhost:5432/{db_name}
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("\n=== 设置完成 ===")
        print(f"数据库名: {db_name}")
        print(f"用户名: {db_user}")
        print(f"主机: localhost")
        print(f"端口: 5432")
        print(f".env文件已创建")
        print("\n请记得在.env文件中设置您的YouTube API密钥!")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    create_database()
