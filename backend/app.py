# backend/app.py
import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
from models import db, Channel
from sqlalchemy import extract
import calendar
from datetime import date

# 加载环境变量
load_dotenv()

# 初始化 Flask 应用
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库和CORS
db.init_app(app)
CORS(app)  # 允许所有来源的跨域请求

@app.route('/api/channels', methods=['GET'])
def get_channels():
    """获取频道列表API，支持多种筛选条件"""
    # --- 1. 获取查询参数 ---
    # 订阅者数量范围
    min_subs = request.args.get('min_subs', type=int)
    max_subs = request.args.get('max_subs', type=int)

    # 创建时间范围 (年份和月份)
    start_year = request.args.get('start_year', type=int)
    start_month = request.args.get('start_month', type=int)
    end_year = request.args.get('end_year', type=int)
    end_month = request.args.get('end_month', type=int)

    # 频道名称模糊查询
    name_query = request.args.get('name', type=str)

    # --- 2. 构建动态查询 ---
    query = Channel.query

    # 订阅者筛选
    if min_subs is not None:
        query = query.filter(Channel.subscriber_count >= min_subs)
    if max_subs is not None:
        query = query.filter(Channel.subscriber_count <= max_subs)

    # 创建时间筛选
    # 精确匹配年份
    if start_year is not None and end_year is not None and start_year == end_year:
        query = query.filter(extract('year', Channel.created_at) == start_year)
        # 月份范围
        if start_month is not None:
            query = query.filter(extract('month', Channel.created_at) >= start_month)
        if end_month is not None:
            query = query.filter(extract('month', Channel.created_at) <= end_month)
    # 跨年份范围(简化处理)
    elif start_year is not None and end_year is not None:
        # 构建开始日期和结束日期
        start_date = date(start_year, start_month or 1, 1)
        # 对于结束日期，我们要包含该月的最后一天
        last_day = calendar.monthrange(end_year, end_month or 12)[1]
        end_date = date(end_year, end_month or 12, last_day)
        query = query.filter(Channel.created_at.between(start_date, end_date))

    # 名称模糊查询
    if name_query:
        # 使用 ilike 实现不区分大小写的模糊匹配
        query = query.filter(Channel.name.ilike(f'%{name_query}%'))

    # --- 3. 执行查询并返回结果 ---
    try:
        channels = query.order_by(Channel.subscriber_count.desc()).all()
        results = [channel.to_dict() for channel in channels]
        return jsonify(results)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "healthy", "message": "YouTube Channel Explorer API is running"})

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取数据库统计信息"""
    try:
        with app.app_context():
            total_channels = Channel.query.count()
            if total_channels > 0:
                max_subs = db.session.query(db.func.max(Channel.subscriber_count)).scalar()
                min_subs = db.session.query(db.func.min(Channel.subscriber_count)).scalar()
                avg_subs = db.session.query(db.func.avg(Channel.subscriber_count)).scalar()
            else:
                max_subs = min_subs = avg_subs = 0

            return jsonify({
                "total_channels": total_channels,
                "max_subscribers": max_subs,
                "min_subscribers": min_subs,
                "avg_subscribers": int(avg_subs) if avg_subs else 0
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    with app.app_context():
        # 在首次运行时创建数据库表
        db.create_all()
        print("Database tables created successfully!")
    app.run(debug=True, port=5000)