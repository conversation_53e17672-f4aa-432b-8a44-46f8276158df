# backend/models.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from sqlalchemy import Index

db = SQLAlchemy()

class Channel(db.Model):
    """YouTube频道模型"""
    __tablename__ = 'channels'

    # 主键字段
    id = db.Column(db.String(50), primary_key=True)  # YouTube频道ID

    # 基本信息字段
    name = db.Column(db.String(200), nullable=False)  # 频道名称
    subscriber_count = db.Column(db.BigInteger, nullable=False, default=0)  # 订阅者数量
    created_at = db.Column(db.DateTime, nullable=False)  # 频道创建时间

    # 系统字段
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 创建索引以优化查询性能
    __table_args__ = (
        # 订阅者数量索引 - 用于范围查询优化
        Index('idx_subscriber_count', 'subscriber_count'),
        # 创建时间索引 - 用于时间范围查询优化
        Index('idx_created_at', 'created_at'),
        # 复合索引 - 用于同时按订阅者数量和创建时间筛选的查询优化
        Index('idx_subscriber_created', 'subscriber_count', 'created_at'),
        # 频道名称索引 - 用于模糊查询优化
        Index('idx_name', 'name'),
    )

    def to_dict(self):
        """将模型转换为字典格式，用于JSON序列化"""
        return {
            'id': self.id,
            'name': self.name,
            'subscriber_count': self.subscriber_count,
            'created_at': self.created_at.strftime('%Y-%m-%d') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    def __repr__(self):
        return f'<Channel {self.name} ({self.subscriber_count} subscribers)>'