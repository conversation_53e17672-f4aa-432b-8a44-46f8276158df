# backend/fetch_youtube_data.py
import os
import googleapiclient.discovery
from datetime import datetime
from dotenv import load_dotenv
from app import app, db, Channel  # 从 app.py 导入 app, db, Channel

load_dotenv()

# --- 配置 ---
YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY')
# 示例频道 ID 列表 (例如: MKBHD, Linus Tech Tips, Google)
CHANNEL_IDS = [
    'UCBJycsmduvYEL83R_U4JriQ',  # MKBHD
    'UCXuqSBlHAE6Xw-yeJA0Tunw',  # Linus Tech Tips
    'UC_x5XG1OV2P6uZZ5FSM9Ttw',  # Google
    'UCsooa4yRKGN_zEE8iknghZA',  # TED-Ed
    'UCJ0-OtVpF0wOKEqT2Z1HEtA',  # ElectroBOOM
    'UC7_YxT-KID8kRbqZo7MyscQ',  # Marques Brownlee
    'UCR-DXc1voovS8nhAvccRZhg',  # <PERSON>rling
    'UCld68syR8Wi-GY_n4CaoJGA',  # <PERSON><PERSON><PERSON>
    # 您可以添加更多频道ID
]

def fetch_and_store_channels():
    """从YouTube API获取频道数据并存储到数据库"""
    if not YOUTUBE_API_KEY:
        print("错误: 请在.env文件中设置YOUTUBE_API_KEY")
        return

    try:
        youtube = googleapiclient.discovery.build(
            "youtube", "v3", developerKey=YOUTUBE_API_KEY)

        # 通过 ID 一次性获取多个频道的信息
        request = youtube.channels().list(
            part="snippet,statistics",
            id=",".join(CHANNEL_IDS)
        )
        response = request.execute()

        with app.app_context():
            for item in response.get('items', []):
                channel_id = item['id']
                existing_channel = Channel.query.get(channel_id)

                # 解析数据
                name = item['snippet']['title']
                subscriber_count = int(item['statistics'].get('subscriberCount', 0))
                # YouTube API 返回的是 ISO 8601 格式的字符串
                created_at_str = item['snippet']['publishedAt']
                created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))

                if existing_channel:
                    # 更新现有数据
                    existing_channel.name = name
                    existing_channel.subscriber_count = subscriber_count
                    existing_channel.created_at = created_at
                    print(f"Updated channel: {name} ({subscriber_count:,} subscribers)")
                else:
                    # 创建新记录
                    new_channel = Channel(
                        id=channel_id,
                        name=name,
                        subscriber_count=subscriber_count,
                        created_at=created_at
                    )
                    db.session.add(new_channel)
                    print(f"Added new channel: {name} ({subscriber_count:,} subscribers)")

            db.session.commit()
            print("Database population complete.")

    except Exception as e:
        print(f"Error fetching YouTube data: {e}")
        if 'app' in locals():
            with app.app_context():
                db.session.rollback()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()  # 确保表已创建
    fetch_and_store_channels()